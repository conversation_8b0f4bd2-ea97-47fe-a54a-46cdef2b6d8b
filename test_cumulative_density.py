#!/usr/bin/env python3
"""
测试累积密度分析方法的修改
"""

import sys
import numpy as np
from pathlib import Path

# 添加当前目录到路径
sys.path.append('.')

try:
    # 导入修改后的分析器
    from probability_density_analysis import ProbabilityDensityAnalyzer
    
    print("✅ 成功导入 ProbabilityDensityAnalyzer")
    
    # 创建分析器实例
    analyzer = ProbabilityDensityAnalyzer(
        data_months=['2024_1'], 
        num_sectors=8, 
        debug=True
    )
    
    print("✅ 成功创建分析器实例")
    
    # 测试新的累积密度计算方法
    print("\n=== 测试累积密度计算方法 ===")
    
    # 创建模拟子区域数据
    test_subregions = [
        {
            'distance_range': (10, 20),
            'center_distance': 15,
            'point_count': 10,
            'area': 100,
            'x_coords': np.array([12, 15, 18]),
            'y_coords': np.array([5, 8, 12]),
            'angle_range': (0, np.pi/4)
        },
        {
            'distance_range': (20, 30),
            'center_distance': 25,
            'point_count': 8,
            'area': 120,
            'x_coords': np.array([22, 25, 28]),
            'y_coords': np.array([15, 18, 22]),
            'angle_range': (0, np.pi/4)
        },
        {
            'distance_range': (30, 40),
            'center_distance': 35,
            'point_count': 5,
            'area': 140,
            'x_coords': np.array([32, 35, 38]),
            'y_coords': np.array([25, 28, 32]),
            'angle_range': (0, np.pi/4)
        }
    ]
    
    # 测试累积密度计算
    cumulative_densities = analyzer._calculate_cumulative_subregion_densities(test_subregions)
    
    print(f"累积密度计算结果数量: {len(cumulative_densities)}")
    
    for i, density_info in enumerate(cumulative_densities):
        print(f"子区域 {i+1}:")
        print(f"  累积点数: {density_info['cumulative_points']}")
        print(f"  累积面积: {density_info['cumulative_area']:.1f}")
        print(f"  累积密度: {density_info['cumulative_density']:.4f}")
        print(f"  密度变化率: {density_info['density_change_rate']:.4f}")
        print(f"  边界距离: {density_info['boundary_distance']:.1f}m")
    
    # 测试寻找最大变化率
    boundary_subregion = analyzer._find_max_cumulative_density_change_subregion(cumulative_densities)
    
    if boundary_subregion:
        print(f"\n找到边界子区域:")
        print(f"  边界距离: {boundary_subregion['boundary_distance']:.1f}m")
        print(f"  变化率: {boundary_subregion.get('change_rate', boundary_subregion.get('density_change_rate', 0)):.4f}")
        print(f"  变化类型: {boundary_subregion.get('change_type', 'unknown')}")
        print(f"  累积密度: {boundary_subregion.get('cumulative_density', 0):.4f}")
    else:
        print("\n⚠️ 未找到边界子区域")
    
    # 测试边界点计算
    if boundary_subregion:
        boundary_point = analyzer._calculate_boundary_point_from_subregion(
            boundary_subregion, 0, np.pi/4
        )
        
        if boundary_point:
            print(f"\n边界点计算结果:")
            print(f"  边界坐标: ({boundary_point['boundary_x']:.1f}, {boundary_point['boundary_y']:.1f})")
            print(f"  边界距离: {boundary_point['boundary_distance']:.1f}m")
            print(f"  边界角度: {np.degrees(boundary_point['boundary_angle']):.1f}°")
            print(f"  置信度: {boundary_point['confidence']:.4f}")
            print(f"  方法: {boundary_point['method']}")
        else:
            print("\n⚠️ 边界点计算失败")
    
    print("\n✅ 累积密度分析方法测试完成")
    
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

print("\n🎉 所有测试通过！")
