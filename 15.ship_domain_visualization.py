"""
船舶领域可视化系统
基于概率密度分析和椭圆拟合结果，生成完整的可视化图表

可视化步骤：
1. 船舶分布 + 最远距离圆 + 坐标轴直径
2. 扇区划分
3. 各扇区累积密度随距离变化
4. 扇区边界标注
5. 船舶领域椭圆

使用方法：
直接修改下面的控制参数，然后运行即可
"""

# ==================== 控制参数 ====================
# 修改这些参数来控制要生成的可视化

# 要可视化的场景（None表示所有场景）
TARGET_SCENE = "交叉_100m以下"  # 例如: "交叉_100m以下", "追越_100-200m", None

# 要生成的步骤（None表示生成综合图，1-5表示单独步骤）
TARGET_STEP = None  # 例如: None, 1, 2, 3, 4, 5

# 是否生成所有场景的完整可视化
GENERATE_ALL = False  # True表示生成所有场景，False表示只生成指定场景

# 是否生成对比图
GENERATE_COMPARISON = True  # True表示生成不同船长的对比图

# 调试模式
DEBUG_MODE = False

# ==================== 可视化设置 ====================
# 统一的最大圆半径（米）
MAX_CIRCLE_RADIUS = 1000

# 坐标轴显示范围（米）
AXIS_RANGE = 1200

# ================================================

import pickle
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import pandas as pd
from scipy import stats
from scipy.ndimage import gaussian_filter1d

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.facecolor'] = 'white'


class ShipDomainVisualizer:
    """船舶领域可视化器"""
    
    def __init__(self, debug=False):
        """初始化可视化器"""
        self.debug = debug
        
        # 数据存储
        self.density_results = {}
        self.sector_boundaries = {}
        self.ellipse_params = {}
        self.length_intervals = []
        
        # 可视化配置
        self.colors = {
            'ship_points': '#1f77b4',      # 蓝色点
            'max_circle': 'black',         # 黑色实线
            'axis_lines': 'red',           # 红色虚线
            'sector_lines': 'black',       # 黑色虚线
            'boundary_points': 'red',      # 红色边界点
            'ellipse': 'hotpink'           # 粉色椭圆
        }
        
        print("🎨 船舶领域可视化器初始化完成")
    
    def load_data(self):
        """加载所有必要的数据"""
        print("\n=== 加载数据 ===")
        
        # 1. 加载概率密度分析结果
        density_file = Path("result/probability_density/sector_boundaries_results.pkl")
        if not density_file.exists():
            print(f"❌ 未找到概率密度分析结果: {density_file}")
            return False
        
        with open(density_file, 'rb') as f:
            density_data = pickle.load(f)
        
        self.density_results = density_data['density_results']
        self.sector_boundaries = density_data['sector_boundaries']
        self.length_intervals = density_data['length_intervals']
        
        print(f"✅ 加载概率密度结果: {len(self.density_results)} 个场景")
        
        # 2. 加载椭圆拟合结果
        ellipse_file = Path("result/ship_domain_fitting/ship_domain_ellipse_params.pkl")
        if not ellipse_file.exists():
            print(f"❌ 未找到椭圆拟合结果: {ellipse_file}")
            return False
        
        with open(ellipse_file, 'rb') as f:
            ellipse_data = pickle.load(f)
        
        self.ellipse_params = ellipse_data['all_ellipse_params']
        
        print(f"✅ 加载椭圆参数: {len(self.ellipse_params)} 个场景")
        
        return True
    
    def create_comprehensive_visualization(self):
        """创建完整的可视化图表"""
        print("\n=== 生成完整可视化 ===")
        
        vis_dir = Path("vis/comprehensive_visualization")
        vis_dir.mkdir(parents=True, exist_ok=True)
        
        # 为每个场景生成可视化
        for key in self.density_results.keys():
            if key in self.sector_boundaries and key in self.ellipse_params:
                print(f"📊 生成 {key} 的可视化...")
                self._create_scene_visualization(key, vis_dir)
        
        print(f"✅ 可视化完成，保存至: {vis_dir}")
    
    def _create_scene_visualization(self, scene_key, vis_dir):
        """为单个场景创建5步可视化"""
        # 获取数据
        density_data = self.density_results[scene_key]
        boundaries = self.sector_boundaries.get(scene_key, {})
        ellipse = self.ellipse_params.get(scene_key, {})
        
        x_coords = density_data['x_coords']
        y_coords = density_data['y_coords']
        sector_analysis = density_data['sector_analysis']
        
        # 创建5个子图
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle(f'{scene_key.replace("_", " - ")} 船舶领域分析', fontsize=16, fontweight='bold')
        
        # 步骤1: 船舶分布 + 最远距离圆 + 坐标轴
        self._plot_step1_ship_distribution(axes[0, 0], x_coords, y_coords)
        
        # 步骤2: 扇区划分
        self._plot_step2_sector_division(axes[0, 1], x_coords, y_coords, sector_analysis)
        
        # 步骤3: 密度随距离变化
        self._plot_step3_density_distance(axes[0, 2], sector_analysis)
        
        # 步骤4: 扇区边界
        self._plot_step4_sector_boundaries(axes[1, 0], x_coords, y_coords, boundaries, sector_analysis)
        
        # 步骤5: 船舶领域椭圆
        self._plot_step5_ship_domain(axes[1, 1], x_coords, y_coords, boundaries, ellipse, sector_analysis)
        
        # 隐藏第6个子图
        axes[1, 2].axis('off')
        
        plt.tight_layout()
        
        # 保存图片
        scenario_type, length_interval = scene_key.split('_', 1)
        filename = f"{scenario_type}_{length_interval}_comprehensive_analysis.png"
        save_path = vis_dir / filename
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"   保存至: {save_path}")
    
    def _plot_step1_ship_distribution(self, ax, x_coords, y_coords):
        """步骤1: 船舶分布 + 最远距离圆 + 坐标轴直径"""
        # 过滤超过1000米的点
        distances = np.sqrt(x_coords**2 + y_coords**2)
        valid_mask = distances <= MAX_CIRCLE_RADIUS
        filtered_x = x_coords[valid_mask]
        filtered_y = y_coords[valid_mask]

        # 绘制船舶分布（蓝色点）
        ax.scatter(filtered_x, filtered_y, c=self.colors['ship_points'], s=2, alpha=0.6)

        # 绘制统一的1000米圆（黑色实线）
        circle = plt.Circle((0, 0), MAX_CIRCLE_RADIUS, fill=False,
                           color=self.colors['max_circle'], linewidth=2)
        ax.add_patch(circle)

        # 绘制坐标轴直径（红色虚线）
        ax.plot([-MAX_CIRCLE_RADIUS, MAX_CIRCLE_RADIUS], [0, 0],
                color=self.colors['axis_lines'], linestyle='--', linewidth=2)
        ax.plot([0, 0], [-MAX_CIRCLE_RADIUS, MAX_CIRCLE_RADIUS],
                color=self.colors['axis_lines'], linestyle='--', linewidth=2)

        # 标记本船位置
        ax.plot(0, 0, 'ko', markersize=8, markerfacecolor='yellow', markeredgewidth=2)

        # 统一的坐标轴范围
        ax.set_xlim(-AXIS_RANGE, AXIS_RANGE)
        ax.set_ylim(-AXIS_RANGE, AXIS_RANGE)
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)
        # 移除标签
        ax.set_xticks([])
        ax.set_yticks([])
    
    def _plot_step2_sector_division(self, ax, x_coords, y_coords, sector_analysis):
        """步骤2: 扇区划分"""
        # 过滤超过1000米的点
        distances = np.sqrt(x_coords**2 + y_coords**2)
        valid_mask = distances <= MAX_CIRCLE_RADIUS
        filtered_x = x_coords[valid_mask]
        filtered_y = y_coords[valid_mask]

        # 绘制船舶分布
        ax.scatter(filtered_x, filtered_y, c=self.colors['ship_points'], s=2, alpha=0.6)

        # 绘制统一的1000米圆（黑色实线）
        circle = plt.Circle((0, 0), MAX_CIRCLE_RADIUS, fill=False,
                           color=self.colors['max_circle'], linewidth=2)
        ax.add_patch(circle)

        # 绘制坐标轴直径（红色虚线）- 和步骤1一样
        ax.plot([-MAX_CIRCLE_RADIUS, MAX_CIRCLE_RADIUS], [0, 0],
                color=self.colors['axis_lines'], linestyle='--', linewidth=2)
        ax.plot([0, 0], [-MAX_CIRCLE_RADIUS, MAX_CIRCLE_RADIUS],
                color=self.colors['axis_lines'], linestyle='--', linewidth=2)

        # 使用36扇区划分
        num_sectors = 36
        sector_angles = [i * 2 * np.pi / num_sectors - np.pi for i in range(num_sectors)]

        # 绘制扇区分割线（黑色虚线），延伸到1000米圆，但避开红色虚线方向
        for angle in sector_angles:
            # 跳过与红色虚线重叠的方向（0度和90度方向，允许±5度误差）
            angle_deg = np.degrees(angle) % 360
            if angle_deg > 180:
                angle_deg -= 360

            # 检查是否与红色虚线重叠（0°, ±90°, 180°方向）
            is_overlapping = (abs(angle_deg) < 5 or abs(angle_deg - 90) < 5 or
                            abs(angle_deg + 90) < 5 or abs(abs(angle_deg) - 180) < 5)

            if not is_overlapping:
                x_end = MAX_CIRCLE_RADIUS * np.cos(angle)
                y_end = MAX_CIRCLE_RADIUS * np.sin(angle)

                ax.plot([0, x_end], [0, y_end],
                       color=self.colors['sector_lines'], linestyle='--', linewidth=1, alpha=0.7)

        # 标记本船位置
        ax.plot(0, 0, 'ko', markersize=8, markerfacecolor='yellow', markeredgewidth=2)

        # 统一的坐标轴范围
        ax.set_xlim(-AXIS_RANGE, AXIS_RANGE)
        ax.set_ylim(-AXIS_RANGE, AXIS_RANGE)
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)
        # 移除标签
        ax.set_xticks([])
        ax.set_yticks([])
    
    def _plot_step3_density_distance(self, ax, sector_analysis):
        """步骤3: 各扇区累积密度随距离变化"""
        # 收集所有扇区的累积密度数据
        colors = plt.cm.Set3(np.linspace(0, 1, len(sector_analysis)))

        plotted_sectors = 0
        for i, (sector_name, sector_data) in enumerate(sector_analysis.items()):
            if (sector_data is not None and
                sector_data.get('boundary_info') is not None and
                'cumulative_densities' in sector_data['boundary_info']):

                cumulative_densities = sector_data['boundary_info']['cumulative_densities']

                if cumulative_densities and len(cumulative_densities) > 1:
                    # 提取距离和累积密度数据
                    distances = [cd['boundary_distance'] for cd in cumulative_densities]
                    densities = [cd['cumulative_density'] for cd in cumulative_densities]

                    # 过滤超过1000米的点
                    valid_indices = [j for j, d in enumerate(distances) if d <= MAX_CIRCLE_RADIUS]
                    if len(valid_indices) > 1:
                        filtered_distances = [distances[j] for j in valid_indices]
                        filtered_densities = [densities[j] for j in valid_indices]

                        # 绘制累积密度曲线
                        ax.plot(filtered_distances, filtered_densities,
                               color=colors[i], linewidth=2, alpha=0.8,
                               label=f'{sector_name}({len(filtered_distances)}点)')

                        # 标记边界点（密度变化率最大的点）
                        boundary_subregion = sector_data['boundary_info'].get('boundary_subregion')
                        if boundary_subregion and boundary_subregion['boundary_distance'] <= MAX_CIRCLE_RADIUS:
                            boundary_dist = boundary_subregion['boundary_distance']
                            boundary_density = boundary_subregion['cumulative_density']

                            ax.plot(boundary_dist, boundary_density, 'ro',
                                   markersize=6, markerfacecolor='red', markeredgewidth=1)

                        plotted_sectors += 1

        if plotted_sectors > 0:
            ax.set_xlabel('距离 (m)', fontsize=10)
            ax.set_ylabel('累积密度', fontsize=10)
            ax.set_xlim(0, MAX_CIRCLE_RADIUS)
            ax.grid(True, alpha=0.3)

            # 添加图例（只显示前几个扇区，避免过于拥挤）
            if plotted_sectors <= 6:
                ax.legend(fontsize=8, loc='upper right')

            ax.set_title('累积密度随距离变化', fontsize=10)
        else:
            # 如果没有累积密度数据，显示提示信息
            ax.text(0.5, 0.5, '无累积密度数据\n请先运行13.probability_density_analysis.py',
                   ha='center', va='center', transform=ax.transAxes, fontsize=12)
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.set_xticks([])
            ax.set_yticks([])

    def _create_sector_density_plots(self, scene_key, sector_analysis, boundaries, save_dir):
        """为每个扇区创建单独的累积密度图 - 蓝色折线图+红色边界点"""
        print(f"   生成各扇区累积密度图...")

        sector_count = 0
        for sector_name, sector_data in sector_analysis.items():
            if (sector_data is not None and
                sector_data.get('boundary_info') is not None and
                'cumulative_densities' in sector_data['boundary_info']):

                cumulative_densities = sector_data['boundary_info']['cumulative_densities']

                if cumulative_densities and len(cumulative_densities) > 2:
                    # 为每个扇区创建单独的图
                    fig, ax = plt.subplots(1, 1, figsize=(10, 6))

                    # 提取累积密度数据
                    distances = [cd['boundary_distance'] for cd in cumulative_densities]
                    densities = [cd['cumulative_density'] for cd in cumulative_densities]

                    # 过滤超过1000米的点
                    valid_indices = [i for i, d in enumerate(distances) if d <= MAX_CIRCLE_RADIUS]
                    if len(valid_indices) < 2:
                        plt.close()
                        continue

                    filtered_distances = [distances[i] for i in valid_indices]
                    filtered_densities = [densities[i] for i in valid_indices]

                    # 绘制累积密度曲线（蓝色折线）
                    ax.plot(filtered_distances, filtered_densities,
                           color='blue', linewidth=2, alpha=0.8, marker='o', markersize=4)

                    # 标注该扇区的边界点（红色点）
                    boundary_subregion = sector_data['boundary_info'].get('boundary_subregion')
                    if boundary_subregion and boundary_subregion['boundary_distance'] <= MAX_CIRCLE_RADIUS:
                        boundary_distance = boundary_subregion['boundary_distance']
                        boundary_density = boundary_subregion['cumulative_density']

                        # 绘制红色边界点
                        ax.plot(boundary_distance, boundary_density, 'ro',
                               markersize=10, markerfacecolor='red', markeredgewidth=2)

                        # 标注距离值和密度变化率
                        change_rate = boundary_subregion.get('density_change_rate', 0)
                        ax.text(boundary_distance, boundary_density * 1.1,
                               f'{boundary_distance:.0f}m\n变化率:{change_rate:.3f}',
                               ha='center', va='bottom', fontsize=10, fontweight='bold', color='red')

                    # 设置坐标轴
                    ax.set_xlim(0, MAX_CIRCLE_RADIUS)
                    ax.set_xlabel('距离 (m)', fontsize=12)
                    ax.set_ylabel('累积密度', fontsize=12)
                    ax.set_title(f'{sector_name} - 累积密度随距离变化', fontsize=14, fontweight='bold')
                    ax.grid(True, alpha=0.3)

                    # 保存单独的扇区图
                    scenario_type, length_interval = scene_key.split('_', 1)
                    filename = f"{scenario_type}_{length_interval}_{sector_name}_cumulative_density.png"
                    save_path = save_dir / filename
                    plt.savefig(save_path, dpi=300, bbox_inches='tight')
                    plt.close()

                    sector_count += 1

        print(f"   生成了 {sector_count} 个扇区累积密度图")

    def _plot_step4_sector_boundaries(self, ax, x_coords, y_coords, boundaries, sector_analysis):
        """步骤4: 扇区边界标注"""
        # 过滤超过1000米的点
        distances = np.sqrt(x_coords**2 + y_coords**2)
        valid_mask = distances <= MAX_CIRCLE_RADIUS
        filtered_x = x_coords[valid_mask]
        filtered_y = y_coords[valid_mask]

        # 绘制船舶分布
        ax.scatter(filtered_x, filtered_y, c=self.colors['ship_points'], s=2, alpha=0.4)

        # 绘制统一的1000米圆（黑色实线）
        circle = plt.Circle((0, 0), MAX_CIRCLE_RADIUS, fill=False,
                           color=self.colors['max_circle'], linewidth=2)
        ax.add_patch(circle)

        # 绘制坐标轴直径（红色虚线）- 和步骤1一样
        ax.plot([-MAX_CIRCLE_RADIUS, MAX_CIRCLE_RADIUS], [0, 0],
                color=self.colors['axis_lines'], linestyle='--', linewidth=2)
        ax.plot([0, 0], [-MAX_CIRCLE_RADIUS, MAX_CIRCLE_RADIUS],
                color=self.colors['axis_lines'], linestyle='--', linewidth=2)

        # 使用36扇区划分
        num_sectors = 36
        sector_angles = [i * 2 * np.pi / num_sectors - np.pi for i in range(num_sectors)]

        # 绘制扇区分割线，延伸到1000米圆，但避开红色虚线方向
        for angle in sector_angles:
            # 跳过与红色虚线重叠的方向
            angle_deg = np.degrees(angle) % 360
            if angle_deg > 180:
                angle_deg -= 360

            # 检查是否与红色虚线重叠
            is_overlapping = (abs(angle_deg) < 5 or abs(angle_deg - 90) < 5 or
                            abs(angle_deg + 90) < 5 or abs(abs(angle_deg) - 180) < 5)

            if not is_overlapping:
                x_end = MAX_CIRCLE_RADIUS * np.cos(angle)
                y_end = MAX_CIRCLE_RADIUS * np.sin(angle)

                ax.plot([0, x_end], [0, y_end],
                       color=self.colors['sector_lines'], linestyle='--', linewidth=1, alpha=0.5)

        # 绘制扇区边界（绿色圆弧）
        self._draw_sector_boundaries_as_arcs(ax, boundaries)

        # 标记本船位置
        ax.plot(0, 0, 'ko', markersize=8, markerfacecolor='yellow', markeredgewidth=2)

        # 统一的坐标轴范围
        ax.set_xlim(-AXIS_RANGE, AXIS_RANGE)
        ax.set_ylim(-AXIS_RANGE, AXIS_RANGE)
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)
        # 移除标签
        ax.set_xticks([])
        ax.set_yticks([])

    def _save_boundary_statistics(self, scene_key, boundaries, save_dir):
        """保存扇区边界距离统计"""
        boundary_stats = []

        for sector_name, boundary_info in boundaries.items():
            boundary_distance = boundary_info['boundary_distance']
            boundary_angle = boundary_info['boundary_angle']
            point_count = boundary_info.get('point_count', 0)
            method = boundary_info.get('method', 'unknown')

            # 转换角度为度数
            angle_degrees = np.degrees(boundary_angle)
            if angle_degrees < 0:
                angle_degrees += 360

            boundary_stats.append({
                'sector_name': sector_name,
                'boundary_distance_m': round(boundary_distance, 1),
                'boundary_angle_deg': round(angle_degrees, 1),
                'point_count': point_count,
                'detection_method': method
            })

        # 保存到CSV文件
        if boundary_stats:
            import pandas as pd
            df = pd.DataFrame(boundary_stats)

            scenario_type, length_interval = scene_key.split('_', 1)
            filename = f"{scenario_type}_{length_interval}_boundary_statistics.csv"
            save_path = save_dir / filename

            df.to_csv(save_path, index=False, encoding='utf-8-sig')
            print(f"   边界统计保存至: {save_path}")

    def _draw_sector_boundaries_as_arcs(self, ax, boundaries, sector_analysis=None, alpha=1.0):
        """将扇区边界绘制为绿色圆弧，按照36扇区划分"""
        try:
            # 使用36扇区划分方式
            num_sectors = 36
            sector_width = 2 * np.pi / num_sectors  # 每个扇区10度

            # 为每个边界绘制对应扇区的圆弧
            for sector_name, boundary_info in boundaries.items():
                boundary_distance = boundary_info['boundary_distance']
                boundary_angle = boundary_info['boundary_angle']

                # 只显示在1000米范围内的边界
                if boundary_distance <= MAX_CIRCLE_RADIUS:
                    # 从扇区名称中提取扇区编号
                    sector_index = self._extract_sector_index(sector_name)

                    if sector_index is not None:
                        # 计算该扇区的角度范围
                        start_angle = sector_index * sector_width - np.pi
                        end_angle = (sector_index + 1) * sector_width - np.pi
                    else:
                        # 如果无法从名称提取，根据边界角度推断
                        sector_index = self._get_sector_index_from_angle(boundary_angle, num_sectors)
                        start_angle = sector_index * sector_width - np.pi
                        end_angle = (sector_index + 1) * sector_width - np.pi

                    # 处理跨越-π/π边界的扇区
                    if start_angle < -np.pi:
                        start_angle += 2 * np.pi
                    if end_angle > np.pi:
                        end_angle -= 2 * np.pi

                    # 生成该扇区范围内的圆弧
                    if start_angle < end_angle:
                        arc_angles = np.linspace(start_angle, end_angle, 15)  # 10度扇区用15个点足够
                    else:
                        # 跨越边界的情况
                        arc_angles1 = np.linspace(start_angle, np.pi, 8)
                        arc_angles2 = np.linspace(-np.pi, end_angle, 8)
                        arc_angles = np.concatenate([arc_angles1, arc_angles2])

                    arc_x = boundary_distance * np.cos(arc_angles)
                    arc_y = boundary_distance * np.sin(arc_angles)

                    # 绘制绿色圆弧
                    ax.plot(arc_x, arc_y, color='green', linewidth=3, alpha=alpha)

        except Exception as e:
            if self.debug:
                print(f"绘制边界圆弧失败: {e}")

    def _extract_sector_index(self, sector_name):
        """从扇区名称中提取扇区编号"""
        try:
            import re
            # 尝试从扇区名称中提取数字
            match = re.search(r'扇区(\d+)', sector_name)
            if match:
                return int(match.group(1)) - 1  # 转换为0-based索引

            # 尝试其他可能的格式
            match = re.search(r'sector(\d+)', sector_name.lower())
            if match:
                return int(match.group(1)) - 1

            return None
        except:
            return None

    def _get_sector_index_from_angle(self, angle, num_sectors):
        """根据角度获取扇区索引"""
        try:
            # 将角度转换到[0, 2π)范围
            normalized_angle = angle + np.pi
            if normalized_angle < 0:
                normalized_angle += 2 * np.pi
            elif normalized_angle >= 2 * np.pi:
                normalized_angle -= 2 * np.pi

            # 计算扇区索引
            sector_width = 2 * np.pi / num_sectors
            sector_index = int(normalized_angle / sector_width)

            # 确保索引在有效范围内
            return max(0, min(num_sectors - 1, sector_index))

        except Exception as e:
            if self.debug:
                print(f"扇区索引计算失败: {e}")
            return 0

    def _plot_step5_ship_domain(self, ax, x_coords, y_coords, boundaries, ellipse, sector_analysis):
        """步骤5: 船舶领域椭圆"""
        # 过滤超过1000米的点
        distances = np.sqrt(x_coords**2 + y_coords**2)
        valid_mask = distances <= MAX_CIRCLE_RADIUS
        filtered_x = x_coords[valid_mask]
        filtered_y = y_coords[valid_mask]

        # 绘制船舶分布（更淡）
        ax.scatter(filtered_x, filtered_y, c=self.colors['ship_points'], s=2, alpha=0.3)

        # 绘制统一的1000米圆（黑色实线，更淡）
        circle = plt.Circle((0, 0), MAX_CIRCLE_RADIUS, fill=False,
                           color=self.colors['max_circle'], linewidth=2, alpha=0.5)
        ax.add_patch(circle)

        # 绘制坐标轴直径（红色虚线）- 和步骤1一样
        ax.plot([-MAX_CIRCLE_RADIUS, MAX_CIRCLE_RADIUS], [0, 0],
                color=self.colors['axis_lines'], linestyle='--', linewidth=2, alpha=0.7)
        ax.plot([0, 0], [-MAX_CIRCLE_RADIUS, MAX_CIRCLE_RADIUS],
                color=self.colors['axis_lines'], linestyle='--', linewidth=2, alpha=0.7)

        # 绘制扇区分割线（更淡）
        # 使用36扇区划分
        num_sectors = 36
        sector_angles = [i * 2 * np.pi / num_sectors - np.pi for i in range(num_sectors)]

        for angle in sector_angles:
            # 跳过与红色虚线重叠的方向
            angle_deg = np.degrees(angle) % 360
            if angle_deg > 180:
                angle_deg -= 360

            # 检查是否与红色虚线重叠
            is_overlapping = (abs(angle_deg) < 5 or abs(angle_deg - 90) < 5 or
                            abs(angle_deg + 90) < 5 or abs(abs(angle_deg) - 180) < 5)

            if not is_overlapping:
                x_end = MAX_CIRCLE_RADIUS * np.cos(angle)
                y_end = MAX_CIRCLE_RADIUS * np.sin(angle)

                ax.plot([0, x_end], [0, y_end],
                       color=self.colors['sector_lines'], linestyle='--', linewidth=1, alpha=0.3)

        # 绘制扇区边界（绿色圆弧，更淡）
        self._draw_sector_boundaries_as_arcs(ax, boundaries, alpha=0.7)

        # 绘制船舶领域椭圆（粉色实线）
        if ellipse and 'a' in ellipse and 'b' in ellipse:
            a, b = ellipse['a'], ellipse['b']

            # 生成椭圆点
            theta = np.linspace(0, 2*np.pi, 100)
            ellipse_x = b * np.cos(theta)  # 横向半轴
            ellipse_y = a * np.sin(theta)  # 纵向半轴

            ax.plot(ellipse_x, ellipse_y, color=self.colors['ellipse'], linewidth=2)

        # 标记本船位置
        ax.plot(0, 0, 'ko', markersize=8, markerfacecolor='yellow', markeredgewidth=2)

        # 统一的坐标轴范围
        ax.set_xlim(-AXIS_RANGE, AXIS_RANGE)
        ax.set_ylim(-AXIS_RANGE, AXIS_RANGE)
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)
        # 移除标签
        ax.set_xticks([])
        ax.set_yticks([])

    def create_summary_comparison(self):
        """创建不同场景和船长的对比图"""
        print("\n=== 生成对比分析 ===")

        vis_dir = Path("vis/comprehensive_visualization")
        vis_dir.mkdir(parents=True, exist_ok=True)

        # 按场景类型分组
        crossing_scenes = {k: v for k, v in self.ellipse_params.items() if k.startswith('交叉')}
        overtaking_scenes = {k: v for k, v in self.ellipse_params.items() if k.startswith('追越')}

        if crossing_scenes:
            self._create_scenario_comparison('交叉避让', crossing_scenes, vis_dir)

        if overtaking_scenes:
            self._create_scenario_comparison('追越避让', overtaking_scenes, vis_dir)

    def _create_scenario_comparison(self, scenario_name, scenes, vis_dir):
        """创建单个场景类型的对比图"""
        n_scenes = len(scenes)
        if n_scenes == 0:
            return

        # 计算子图布局
        cols = min(3, n_scenes)
        rows = (n_scenes + cols - 1) // cols

        fig, axes = plt.subplots(rows, cols, figsize=(6*cols, 6*rows))
        if n_scenes == 1:
            axes = [axes]
        elif rows == 1:
            axes = [axes]
        else:
            axes = axes.flatten()

        fig.suptitle(f'{scenario_name}不同船长的船舶领域对比', fontsize=16, fontweight='bold')

        for i, (scene_key, ellipse) in enumerate(scenes.items()):
            ax = axes[i] if n_scenes > 1 else axes[0]

            # 获取数据
            if scene_key in self.density_results:
                density_data = self.density_results[scene_key]
                x_coords = density_data['x_coords']
                y_coords = density_data['y_coords']

                # 绘制数据点
                ax.scatter(x_coords, y_coords, c=self.colors['ship_points'], s=1, alpha=0.4)

            # 绘制椭圆
            if 'a' in ellipse and 'b' in ellipse:
                a, b = ellipse['a'], ellipse['b']

                theta = np.linspace(0, 2*np.pi, 100)
                ellipse_x = b * np.cos(theta)
                ellipse_y = a * np.sin(theta)

                ax.plot(ellipse_x, ellipse_y, color=self.colors['ellipse'], linewidth=3)

                # 标注
                _, length_interval = scene_key.split('_', 1)
                ax.set_title(f'{length_interval}\na={a:.0f}m, b={b:.0f}m')

            # 标记本船
            ax.plot(0, 0, 'ko', markersize=6, markerfacecolor='yellow')

            ax.set_aspect('equal')
            ax.grid(True, alpha=0.3)
            ax.set_xlabel('横向距离 (m)')
            ax.set_ylabel('纵向距离 (m)')

        # 隐藏多余的子图
        for i in range(n_scenes, len(axes)):
            axes[i].axis('off')

        plt.tight_layout()

        # 保存
        filename = f"{scenario_name}_comparison.png"
        save_path = vis_dir / filename
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"📊 {scenario_name}对比图保存至: {save_path}")

    def create_single_visualization(self, scene_key, step=None, save_dir=None):
        """创建单个场景的指定步骤可视化

        Args:
            scene_key: 场景键名，如 "交叉_100m以下"
            step: 可视化步骤 (1-5)，None表示生成所有步骤
            save_dir: 保存目录，None使用默认目录
        """
        print(f"\n=== 生成单独可视化: {scene_key} ===")

        if scene_key not in self.density_results:
            print(f"❌ 未找到场景数据: {scene_key}")
            available_scenes = list(self.density_results.keys())
            print(f"可用场景: {available_scenes}")
            return False

        # 设置保存目录
        if save_dir is None:
            save_dir = Path("vis/single_visualization")
        else:
            save_dir = Path(save_dir)
        save_dir.mkdir(parents=True, exist_ok=True)

        # 获取数据
        density_data = self.density_results[scene_key]
        boundaries = self.sector_boundaries.get(scene_key, {})
        ellipse = self.ellipse_params.get(scene_key, {})

        x_coords = density_data['x_coords']
        y_coords = density_data['y_coords']
        sector_analysis = density_data['sector_analysis']

        if step is None:
            # 生成所有步骤的综合图
            self._create_single_comprehensive_plot(scene_key, x_coords, y_coords,
                                                 boundaries, ellipse, sector_analysis, save_dir)
        else:
            # 生成指定步骤的单独图
            self._create_single_step_plot(scene_key, step, x_coords, y_coords,
                                        boundaries, ellipse, sector_analysis, save_dir)

        return True

    def _create_single_comprehensive_plot(self, scene_key, x_coords, y_coords,
                                        boundaries, ellipse, sector_analysis, save_dir):
        """创建单个场景的综合图（所有5个步骤）"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))

        # 步骤1-5（移除所有标题）
        self._plot_step1_ship_distribution(axes[0, 0], x_coords, y_coords)

        self._plot_step2_sector_division(axes[0, 1], x_coords, y_coords, sector_analysis)

        self._plot_step3_density_distance(axes[0, 2], sector_analysis)

        self._plot_step4_sector_boundaries(axes[1, 0], x_coords, y_coords, boundaries, sector_analysis)

        self._plot_step5_ship_domain(axes[1, 1], x_coords, y_coords, boundaries, ellipse, sector_analysis)

        # 隐藏第6个子图
        axes[1, 2].axis('off')

        plt.tight_layout()

        # 保存综合图
        scenario_type, length_interval = scene_key.split('_', 1)
        filename = f"{scenario_type}_{length_interval}_comprehensive.png"
        save_path = save_dir / filename
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"📊 综合图保存至: {save_path}")

        # 生成各扇区的单独密度图
        self._create_sector_density_plots(scene_key, sector_analysis, boundaries, save_dir)

        # 保存边界统计
        self._save_boundary_statistics(scene_key, boundaries, save_dir)

    def _create_single_step_plot(self, scene_key, step, x_coords, y_coords,
                               boundaries, ellipse, sector_analysis, save_dir):
        """创建单个步骤的独立图"""
        fig, ax = plt.subplots(1, 1, figsize=(10, 10))

        step_names = {
            1: "船舶分布+最远圆+坐标轴",
            2: "扇区划分",
            3: "累积密度随距离变化",
            4: "扇区边界标注",
            5: "船舶领域椭圆"
        }

        if step == 1:
            self._plot_step1_ship_distribution(ax, x_coords, y_coords)
        elif step == 2:
            self._plot_step2_sector_division(ax, x_coords, y_coords, sector_analysis)
        elif step == 3:
            self._plot_step3_density_distance(ax, sector_analysis)
            # 对于步骤3，还要生成单独的扇区密度图
            self._create_sector_density_plots(scene_key, sector_analysis, boundaries, save_dir)
        elif step == 4:
            self._plot_step4_sector_boundaries(ax, x_coords, y_coords, boundaries, sector_analysis)
        elif step == 5:
            self._plot_step5_ship_domain(ax, x_coords, y_coords, boundaries, ellipse, sector_analysis)
        else:
            print(f"❌ 无效的步骤编号: {step}，有效范围: 1-5")
            plt.close()
            return False

        ax.set_title(f'{scene_key.replace("_", " - ")} - 步骤{step}: {step_names[step]}',
                    fontsize=14, fontweight='bold')

        plt.tight_layout()

        # 保存
        scenario_type, length_interval = scene_key.split('_', 1)
        filename = f"{scenario_type}_{length_interval}_step{step}.png"
        save_path = save_dir / filename
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"📊 步骤{step}图保存至: {save_path}")
        return True

    def list_available_scenes(self):
        """列出所有可用的场景"""
        print("\n=== 可用场景列表 ===")

        if not self.density_results:
            print("❌ 未加载任何场景数据")
            return []

        scenes = list(self.density_results.keys())

        print("场景键名:")
        for i, scene in enumerate(scenes, 1):
            scenario_type, length_interval = scene.split('_', 1)
            data_count = len(self.density_results[scene]['x_coords'])
            has_boundaries = scene in self.sector_boundaries
            has_ellipse = scene in self.ellipse_params

            status = "✅" if (has_boundaries and has_ellipse) else "⚠️"

            print(f"  {i:2d}. {scene:20} ({scenario_type} - {length_interval}) "
                  f"[{data_count:4d}点] {status}")

        print(f"\n总计: {len(scenes)} 个场景")
        print("✅ = 完整数据 (密度+边界+椭圆)")
        print("⚠️ = 数据不完整")

        return scenes

    def run_full_visualization(self):
        """运行完整的可视化流程"""
        print("🎨 开始船舶领域可视化...")
        print("=" * 60)

        try:
            # 加载数据
            if not self.load_data():
                return False

            # 生成完整可视化
            self.create_comprehensive_visualization()

            # 生成对比分析
            self.create_summary_comparison()

            print("\n" + "=" * 60)
            print("🎉 船舶领域可视化完成！")
            print("📁 输出目录: vis/comprehensive_visualization/")
            print("=" * 60)

            return True

        except Exception as e:
            print(f"\n❌ 可视化失败: {e}")
            if self.debug:
                import traceback
                traceback.print_exc()
            return False


def main():
    """主函数 - 使用控制参数"""
    print("🎨 船舶领域可视化系统")
    print("=" * 50)

    # 显示当前控制参数
    print("📋 当前控制参数:")
    print(f"   目标场景: {TARGET_SCENE or '所有场景'}")
    print(f"   目标步骤: {TARGET_STEP or '综合图(所有步骤)'}")
    print(f"   生成所有: {'是' if GENERATE_ALL else '否'}")
    print(f"   生成对比: {'是' if GENERATE_COMPARISON else '否'}")
    print("=" * 50)

    # 创建可视化器
    visualizer = ShipDomainVisualizer(debug=DEBUG_MODE)

    # 加载数据
    if not visualizer.load_data():
        print("❌ 数据加载失败")
        return

    # 根据控制参数执行相应操作
    if GENERATE_ALL:
        # 生成所有场景的完整可视化
        print("\n🎨 生成所有场景的完整可视化...")
        success = visualizer.run_full_visualization()
        if success:
            print("✅ 所有场景可视化生成成功")
        else:
            print("❌ 可视化生成失败")

    elif TARGET_SCENE:
        # 生成指定场景的可视化
        print(f"\n🎯 生成指定场景可视化: {TARGET_SCENE}")
        success = visualizer.create_single_visualization(TARGET_SCENE, TARGET_STEP)
        if success:
            if TARGET_STEP is None:
                print(f"✅ {TARGET_SCENE} 的综合可视化生成成功")
            else:
                print(f"✅ {TARGET_SCENE} 的步骤{TARGET_STEP}可视化生成成功")
        else:
            print(f"❌ {TARGET_SCENE} 的可视化生成失败")

    else:
        # 显示可用场景列表
        print("\n📋 未指定目标场景，显示可用场景:")
        visualizer.list_available_scenes()
        print("\n💡 提示: 修改代码顶部的 TARGET_SCENE 参数来指定要可视化的场景")

    # 生成对比图
    if GENERATE_COMPARISON and (GENERATE_ALL or not TARGET_SCENE):
        print("\n📊 生成对比分析图...")
        visualizer.create_summary_comparison()

    print("\n🎉 可视化任务完成！")
    print("📁 输出目录:")
    if GENERATE_ALL or not TARGET_SCENE:
        print("   • vis/comprehensive_visualization/ (完整分析)")
    if TARGET_SCENE:
        print("   • vis/single_visualization/ (单独场景)")

    print("\n💡 修改提示:")
    print("   要生成其他场景，请修改代码顶部的控制参数：")
    print("   • TARGET_SCENE: 指定场景名称")
    print("   • TARGET_STEP: 指定步骤(1-5)或None(综合图)")
    print("   • GENERATE_ALL: True生成所有场景")
    print("   • GENERATE_COMPARISON: True生成对比图")


if __name__ == '__main__':
    main()
